name: Deploy to Dev Environment on Merge

on:
  pull_request:
    types: [closed]
    branches: [main, master]

env:
  GCP_REGION: us-central1
  GCP_GCR_HOST: docker.pkg.dev
  MIN_INSTANCES: 0
  MAX_INSTANCES: 30
  CONCURRENCY: 500

jobs:
  changes:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    outputs:
      backend: ${{ steps.filter.outputs.backend }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      - uses: actions/checkout@v3
      
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            backend:
              - 'backend/**'
            frontend:
              - 'src/**'
              - 'public/**'
              - 'package.json'
              - 'yarn.lock'
              - 'package-lock.json'
              - 'tsconfig.json'
              - 'vite.config.*'
              - '.eslintrc*'
              - '.prettierrc*'
              - '*.{js,jsx,ts,tsx,json,yml,yaml}'

  deploy-backend-dev:
    needs: changes
    if: needs.changes.outputs.backend == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - name: tooler
            dockerfile: ./cmd/tooler-api/dockerfile
            app_name_secret: GCP_APP_NAME
          - name: tooler-equipment-uploader
            dockerfile: ./cmd/tooler-equipment-uploader/Dockerfile
            app_name: tooler-equipment-uploader
          - name: derental-crone-job
            dockerfile: ./cmd/tooler-cron-jobs/Dockerfile
            app_name: derental-crone-job
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v0.2.0
        with:
          project_id: ${{ secrets.GCP_PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_CREDENTIALS }}

      - name: Configure Docker
        run: gcloud auth configure-docker ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }} --quiet

      - name: Build Docker image
        run: |
          cd backend
          docker build -t ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }}/${{ secrets.GCP_PROJECT_ID }}/cloud-run-source-deploy/${{ matrix.service.name }}:${{ github.sha }} -f ${{ matrix.service.dockerfile }} .

      - name: Push Docker image
        run: docker push ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }}/${{ secrets.GCP_PROJECT_ID }}/cloud-run-source-deploy/${{ matrix.service.name }}:${{ github.sha }}

      - name: Deploy to Cloud Run
        run: |
          APP_NAME=${{ matrix.service.app_name || secrets[matrix.service.app_name_secret] }}
          gcloud run deploy ${APP_NAME} \
            --image ${{ env.GCP_REGION }}-${{ env.GCP_GCR_HOST }}/${{ secrets.GCP_PROJECT_ID }}/cloud-run-source-deploy/${{ matrix.service.name }}:${{ github.sha }} \
            --region ${{ env.GCP_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --project ${{ secrets.GCP_PROJECT_ID }} \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances ${{ env.MAX_INSTANCES }} \
            --execution-environment gen2 \
            --concurrency ${{ env.CONCURRENCY }} \
            --set-env-vars MODE=production \
            --set-env-vars GOOGLE_CLOUD_PROJECT=${{ secrets.GCP_PROJECT_ID }} \
            --set-env-vars FIREBASE_API_KEY=${{ secrets.FIREBASE_API_KEY }} \
            --set-env-vars FIREBASE_STORAGE_BUCKET=${{ secrets.FIREBASE_STORAGE_BUCKET }} \
            --set-env-vars EQUIPMENT_LIBRARY_URL=${{ secrets.EQUIPMENT_LIBRARY_URL }} \
            --set-env-vars FRONTEND_URL=${{ secrets.FRONTEND_URL }} \
            --set-env-vars AIRTABLE_API_KEY=${{ secrets.AIRTABLE_API_KEY }} \
            --set-env-vars SLACK_WEBHOOK_URL=${{ secrets.SLACK_WEBHOOK_URL }} \
            --set-env-vars SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }} \
            --set-env-vars SENDGRID_SENDER=${{ secrets.SENDGRID_SENDER }} \
            --set-env-vars AWS_REGION=${{ secrets.AWS_REGION }} \
            --set-env-vars AWS_ACCESS_KEY=${{ secrets.AWS_ACCESS_KEY }} \
            --set-env-vars AWS_SECRET_KEY=${{ secrets.AWS_SECRET_KEY }} \
            --set-env-vars BOOKING_NOTIFICATION_EMAIL=${{ secrets.BOOKING_NOTIFICATION_EMAIL }} \
            --set-env-vars STRIPE_API_KEY=${{ secrets.STRIPE_API_KEY }} \
            --set-env-vars STRIPE_WEBHOOK_SECRET=${{ secrets.STRIPE_WEBHOOK_SECRET }} \
            --set-env-vars TEMPLATR_API_KEY=${{ secrets.TEMPLATR_API_KEY }} \
            --set-env-vars GOOGLE_TRANSLATION_API_KEY=${{ secrets.GOOGLE_TRANSLATION_API_KEY }} \
            --set-env-vars CALIFORNIA_TAX_RATE_ID=${{ secrets.TEST_CALIFORNIA_TAX_RATE_ID }} \
            --set-env-vars NEVADA_TAX_RATE_ID=${{ secrets.TEST_NEVADA_TAX_RATE_ID }} \
            --set-env-vars ARIZONA_TAX_RATE_ID=${{ secrets.TEST_ARIZONA_TAX_RATE_ID }} \
            --set-env-vars TEXAS_TAX_RATE_ID=${{ secrets.TEST_TEXAS_TAX_RATE_ID }} \
            --set-env-vars SAR_TAX_RATE_ID=${{ secrets.TEST_SAR_TAX_RATE_ID }} \
            --set-env-vars OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY }} \

  deploy-frontend-dev:
    needs: changes
    if: needs.changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Create env file
        run: |
          cat << EOF > .env
            VITE_REACT_APP_BASE_URL=${{ secrets.REACT_APP_BASE_URL }}
            VITE_ALGOLIA_ID=${{ secrets.ALGOLIA_ID }}
            VITE_ALGOLIA_API_KEY=${{ secrets.ALGOLIA_API_KEY }}
            VITE_ALGOLIA_INDEX_NAME=${{ secrets.DEV_ALGOLIA_INDEX_NAME }}
            VITE_ALGOLIA_INDEX_NAME_STANDARDS=${{ secrets.DEV_ALGOLIA_INDEX_NAME_STANDARDS }}
            VITE_DERENTAL_BIDZ_EMAIL=${{ secrets.DERENTAL_BIDZ_EMAIL }}
            VITE_DERENTAL_SUPER_ADMIN_PASSWORD=${{ secrets.DERENTAL_SUPER_ADMIN_PASSWORD }}
            VITE_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH=${{ secrets.DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH }}
            VITE_ALGOLIA_INDEX_COVERAGE_AREAS_NAME=${{ secrets.ALGOLIA_INDEX_COVERAGE_AREAS_NAME }}
          EOF

      - name: Build
        run: yarn run build

      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
          expires: 30d
          projectId: '${{ secrets.FIREBASE_PROJECT_ID }}'
          channelId: live