import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import {
  Container,
  Row,
  Col,
  Card,
  Button,
  Alert,
  Spinner
} from 'react-bootstrap';
import { MdSave as SaveIcon, MdCancel as CancelIcon, MdUpload as UploadIcon } from 'react-icons/md';
import axios from 'axios';
import { getCookies } from '../../shared/helpers/Cookies';
import styles from './MasterEquipmentUpdate.module.css';

// Simple debounce function
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const validationSchema = Yup.object().shape({
  name_en: Yup.string()
    .trim()
    .required('English name is required')
    .min(2, 'English name must be at least 2 characters')
    .max(255, 'English name must be less than 255 characters'),
  name_fr: Yup.string()
    .trim()
    .min(2, 'French name must be at least 2 characters')
    .max(255, 'French name must be less than 255 characters'),
  description: Yup.string()
    .max(1000, 'Description must be less than 1000 characters'),
  description_en: Yup.string()
    .max(1000, 'English description must be less than 1000 characters'),
  description_fr: Yup.string()
    .max(1000, 'French description must be less than 1000 characters'),
  brand: Yup.string()
    .max(100, 'Brand must be less than 100 characters'),
  model: Yup.string()
    .max(100, 'Model must be less than 100 characters'),
  alias: Yup.object().shape({
    en: Yup.array().of(Yup.string()),
    fr: Yup.array().of(Yup.string())
  }),
  image_link: Yup.string()
    .url('Must be a valid URL')
    .nullable(),
  ara_level1_id: Yup.number()
    .min(0, 'ARA Level 1 ID must be a positive number'),
  ara_level2_id: Yup.number()
    .min(0, 'ARA Level 2 ID must be a positive number'),
  ara_level1_name: Yup.string()
    .max(100, 'ARA Level 1 name must be less than 100 characters'),
  ara_level2_name: Yup.string()
    .max(100, 'ARA Level 2 name must be less than 100 characters')
});

function MasterEquipmentUpdateModern() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [equipment, setEquipment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);
  const [classifying, setClassifying] = useState(false);
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [generatingDescription, setGeneratingDescription] = useState(false);
  const [descriptionError, setDescriptionError] = useState('');
  const [generatingFrenchName, setGeneratingFrenchName] = useState(false);
  const [frenchNameError, setFrenchNameError] = useState('');
  const [nameValidation, setNameValidation] = useState({
    nameEN: { checking: false, available: null, message: '' },
    nameFR: { checking: false, available: null, message: '' }
  });
  const setFieldValueRef = useRef(null);
  const [originalNames, setOriginalNames] = useState({
    name_en: '',
    name_fr: ''
  });

  const baseURL = import.meta.env.VITE_REACT_APP_BASE_URL;

  // Function to check name availability
  const checkNameAvailability = async (nameEN, nameFR, excludeID = id) => {
    if (!nameEN.trim() && !nameFR.trim()) return;

    setNameValidation(prev => ({
      nameEN: { ...prev.nameEN, checking: true },
      nameFR: { ...prev.nameFR, checking: true }
    }));

    try {
      const token = getCookies('adminToken');
      const apiBaseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5051';

      const response = await axios.post(`${apiBaseURL}/admin/master-equipment/check-names`, {
        name_en: nameEN.trim(),
        name_fr: nameFR.trim(),
        exclude_id: excludeID
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setNameValidation({
        nameEN: {
          checking: false,
          available: response.data.name_en_available,
          message: response.data.name_en_available ? '' : 'Equipment with this English name already exists'
        },
        nameFR: {
          checking: false,
          available: response.data.name_fr_available,
          message: response.data.name_fr_available ? '' : 'Equipment with this French name already exists'
        }
      });
    } catch (error) {
      setNameValidation({
        nameEN: { checking: false, available: null, message: '' },
        nameFR: { checking: false, available: null, message: '' }
      });
    }
  };

  const initialValues = equipment ? {
    name_en: equipment.name_en || '',
    name_fr: equipment.name_fr || '',
    brand: equipment.brand || '',
    model: equipment.model || '',
    image_link: equipment.image_link || '',
    description: equipment.description || '',
    description_en: equipment.description_en || '',
    description_fr: equipment.description_fr || '',
    alias: {
      en: equipment.alias?.en || [],
      fr: equipment.alias?.fr || []
    },
    ara_level1_id: equipment.ara_level1_id || 0,
    ara_level2_id: equipment.ara_level2_id || 0,
    ara_level1_name: equipment.ara_level1_name || '',
    ara_level2_name: equipment.ara_level2_name || ''
  } : {
    name_en: '',
    name_fr: '',
    brand: '',
    model: '',
    image_link: '',
    description: '',
    description_en: '',
    description_fr: '',
    alias: { en: [], fr: [] },
    ara_level1_id: 0,
    ara_level2_id: 0,
    ara_level1_name: '',
    ara_level2_name: ''
  };

  // Clear validation messages and reset state on component mount
  useEffect(() => {
    // Reset all validation states on page load/reload
    setNameValidation({
      nameEN: { checking: false, available: null, message: '' },
      nameFR: { checking: false, available: null, message: '' }
    });
    setError('');
    setSuccess('');
  }, []);

  // Debounced validation function that only triggers when names have changed from original
  const debouncedValidation = useCallback(
    debounce((nameEN, nameFR) => {
      // Check if names have actually changed from original values
      const nameENChanged = nameEN.trim() !== originalNames.name_en.trim();
      const nameFRChanged = nameFR.trim() !== originalNames.name_fr.trim();

      // Only validate if names have changed and are not empty
      if ((nameENChanged && nameEN.trim()) || (nameFRChanged && nameFR.trim())) {
        checkNameAvailability(nameEN, nameFR, id);
      } else {
        // Clear validation if names haven't changed or are empty
        setNameValidation({
          nameEN: { checking: false, available: null, message: '' },
          nameFR: { checking: false, available: null, message: '' }
        });
      }
    }, 500),
    [id, originalNames.name_en, originalNames.name_fr]
  );

  useEffect(() => {
    fetchEquipment();
  }, [id]);

  const fetchEquipment = async () => {
    try {
      setLoading(true);
      setError(''); // Clear previous errors
      const token = getCookies('adminToken');
      if (!token) {
        setError('Authentication required. Redirecting to login...');
        setTimeout(() => {
          navigate('/admin/login');
        }, 2000);
        return;
      }
      const response = await axios.get(`${baseURL}/admin/master-equipment/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      setEquipment(response.data);
      // Store original names for comparison
      setOriginalNames({
        name_en: response.data.name_en || '',
        name_fr: response.data.name_fr || ''
      });
    } catch (err) {
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
      } else if (err.response?.status === 403) {
        setError('Access denied. Admin privileges required.');
      } else if (err.response?.status === 404) {
        setError(`Equipment with ID "${id}" not found.`);
      } else if (err.response?.status >= 500) {
        setError('Server error. Please try again later.');
      } else if (err.code === 'NETWORK_ERROR' || !err.response) {
        setError('Network error. Please check your connection and ensure the backend server is running.');
      } else {
        setError(`Failed to load equipment data: ${err.response?.data?.error || err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      setSaving(true);
      setError('');

      // Check if names have changed and if they have validation errors
      const nameENChanged = values.name_en.trim() !== originalNames.name_en.trim();
      const nameFRChanged = values.name_fr.trim() !== originalNames.name_fr.trim();

      // Only block submission if names have changed AND there are validation errors
      if ((nameENChanged && nameValidation.nameEN.available === false) ||
          (nameFRChanged && nameValidation.nameFR.available === false)) {
        setError('Please resolve name availability issues before saving.');
        setSubmitting(false);
        return;
      }

      const token = getCookies('adminToken');
      if (!token) {
        setError('Authentication required. Please log in again.');
        setSubmitting(false);
        return;
      }

      await axios.put(`${baseURL}/admin/master-equipment/${id}`, values, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Master equipment updated successfully!');
      setTimeout(() => {
        navigate('/admin/dashboard');
      }, 2000);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to update equipment');
    } finally {
      setSaving(false);
      setSubmitting(false);
    }
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        setError('Image file size must be less than 5MB');
        return;
      }
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }
      setError(''); // Clear any previous errors
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return;

    try {
      setUploadingImage(true);
      setError(''); // Clear any previous errors

      const formData = new FormData();
      formData.append('file', imageFile);

      const token = getCookies('adminToken');
      const response = await axios.post(`${baseURL}/admin/master-equipment/${id}/image`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const imageUrl = response.data.image_url || response.data.url || response.data.imageUrl || response.data.image_link;

      if (!imageUrl) {
        setError('Image uploaded but no URL returned from server');
        return;
      }

      setEquipment(prev => ({ ...prev, image_link: imageUrl }));
      // Update Formik field value as well
      if (setFieldValueRef.current) {
        setFieldValueRef.current('image_link', imageUrl);
      }
      setImageFile(null);
      setImagePreview(null);
      // Reset the file input
      const fileInput = document.getElementById('image-upload');
      if (fileInput) {
        fileInput.value = '';
      }
      setSuccess('Image uploaded successfully!');
    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Failed to upload image');
    } finally {
      setUploadingImage(false);
    }
  };

  // Check if equipment has existing classification
  const hasClassification = (equipment) => {
    return equipment && (
      (equipment.ara_level1_id && equipment.ara_level1_id > 0) ||
      (equipment.ara_level2_id && equipment.ara_level2_id > 0) ||
      (equipment.ara_level1_name && equipment.ara_level1_name.trim() !== '') ||
      (equipment.ara_level2_name && equipment.ara_level2_name.trim() !== '')
    );
  };
  const classifyEquipment = async () => {
    try {
      setClassifying(true);
      setError('');

      const token = getCookies('adminToken');
      await axios.post(`${baseURL}/admin/master-equipment/${id}/classify`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      setSuccess('Equipment classified successfully! ✅');

      // Refresh the equipment data to get updated classification
      await fetchEquipment();
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to classify equipment');
    } finally {
      setClassifying(false);
    }
  };

  const generateDescription = async (setFieldValue, values) => {
    if (!values.name_en) {
      setDescriptionError('Equipment name is required to generate bilingual descriptions');
      return;
    }

    try {
      setGeneratingDescription(true);
      setDescriptionError('');

      const token = getCookies('adminToken');
      const response = await axios.post(`${baseURL}/admin/generate-description`, {
        equipment_name: values.name_en,
        image_url: values.image_link || ''
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        // Update description fields with the generated bilingual descriptions
        setFieldValue('description', response.data.description_en); // Use English as general description
        setFieldValue('description_en', response.data.description_en);
        setFieldValue('description_fr', response.data.description_fr);
        setSuccess('Bilingual descriptions generated successfully!');
      } else {
        setDescriptionError(response.data.error || 'Failed to generate description');
      }
    } catch (err) {
      setDescriptionError(err.response?.data?.error || 'Failed to generate description');
    } finally {
      setGeneratingDescription(false);
    }
  };

  const generateFrenchName = async (setFieldValue, values) => {
    if (!values.name_en) {
      setFrenchNameError('English name is required to generate French name');
      return;
    }

    try {
      setGeneratingFrenchName(true);
      setFrenchNameError('');

      const token = getCookies('adminToken');
      const response = await axios.post(`${baseURL}/admin/generate-french-name`, {
        english_name: values.name_en
      }, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        // Update French name field with the generated name
        setFieldValue('name_fr', response.data.french_name);
        setSuccess('French name generated successfully!');
      } else {
        setFrenchNameError(response.data.error || 'Failed to generate French name');
      }
    } catch (err) {
      setFrenchNameError(err.response?.data?.error || 'Failed to generate French name');
    } finally {
      setGeneratingFrenchName(false);
    }
  };

  if (loading) {
    return (
      <Container className={`d-flex justify-content-center align-items-center ${styles.loadingContainer}`}>
        <Spinner animation="border" variant="primary" />
      </Container>
    );
  }

  return (
    <Container fluid className="p-0">
      {/* Page Header */}
      <div className={`w-100 ${styles.pageHeader}`}>
        <Container>
          <div className={styles.headerContent}>
            <div className="d-flex align-items-center">
              {/* Derental Logo */}
              <div className={styles.logoSection}>
                <span className={styles.logoText}>
                  <span className={styles.logoHighlight}>
                    De
                  </span>
                  rental
                </span>
              </div>
              <h4 className={`mb-0 ${styles.pageTitle}`}>Update Master Equipment</h4>
            </div>
            <div className={styles.userInfo}>
              <div className={styles.userDetails}>
                <div className={styles.userLabel}>
                  Logged in as:
                </div>
                <div className={styles.userEmail}><EMAIL></div>
              </div>
              <Button
                variant="outline-light"
                size="sm"
                onClick={() => navigate('/admin/dashboard')}
                className={styles.backButton}
              >
                Back to Dashboard
              </Button>
            </div>
          </div>
        </Container>
      </div>

      <Container className="mt-4">
        {error && (
          <Alert variant="danger" className="mb-4">
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" className="mb-4">
            {success}
          </Alert>
        )}

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue, isSubmitting, handleSubmit: formikHandleSubmit, submitForm }) => {
            // Store setFieldValue in a ref so it can be accessed in uploadImage
            setFieldValueRef.current = setFieldValue;

            return (
            <Form onSubmit={formikHandleSubmit}>
              <Row>
                {/* Basic Information */}
                <Col xs={12}>
                  <Card className={styles.card}>
                    <Card.Body className={styles.cardBody}>
                      <h5 className={styles.sectionTitle}>
                        📝 Basic Information
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            English Name *
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl} ${
                              (nameValidation.nameEN.available === false && nameValidation.nameEN.message) ||
                              (touched.name_en && errors.name_en) ? styles.isInvalid :
                              (nameValidation.nameEN.available === true && values.name_en.trim()) ? styles.isValid : ''
                            }`}
                            name="name_en"
                            value={values.name_en}
                            onChange={(e) => {
                              handleChange(e);
                              // Trigger debounced validation
                              debouncedValidation(e.target.value, values.name_fr);
                            }}
                            onBlur={handleBlur}
                          />
                          {nameValidation.nameEN.checking && (
                            <div className={`d-flex align-items-center mt-1 ${styles.validationSpinner}`}>
                              <div className={`spinner-border spinner-border-sm me-2 ${styles.validationSpinnerIcon}`} role="status">
                                <span className="visually-hidden">Loading...</span>
                              </div>
                              Checking availability...
                            </div>
                          )}
                          {nameValidation.nameEN.available === false && nameValidation.nameEN.message ? (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {nameValidation.nameEN.message}
                            </div>
                          ) : nameValidation.nameEN.available === true && values.name_en.trim() && !nameValidation.nameEN.checking ? (
                            <div className={`valid-feedback d-block ${styles.validFeedback}`}>
                              English name is available
                            </div>
                          ) : touched.name_en && errors.name_en && (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {errors.name_en}
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            French Name *
                          </label>
                          {(!values.name_fr || values.name_fr.trim() === '') ? (
                            <div className="input-group">
                              <input
                                type="text"
                                className={`form-control ${styles.formControl} ${
                                  (nameValidation.nameFR.available === false && nameValidation.nameFR.message) ||
                                  (touched.name_fr && errors.name_fr) ? styles.isInvalid :
                                  (nameValidation.nameFR.available === true && values.name_fr.trim()) ? styles.isValid : ''
                                }`}
                                name="name_fr"
                                value={values.name_fr}
                                onChange={(e) => {
                                  handleChange(e);
                                  // Trigger debounced validation
                                  debouncedValidation(values.name_en, e.target.value);
                                }}
                                onBlur={handleBlur}
                              />
                              <Button
                                variant="outline-info"
                                onClick={() => generateFrenchName(setFieldValue, values)}
                                disabled={!values.name_en || generatingFrenchName}
                                title="Generate French name using AI"
                              >
                                <span className={generatingFrenchName ? styles.spinningRobot : ''}>
                                  🤖
                                </span>
                              </Button>
                            </div>
                          ) : (
                            <input
                              type="text"
                              className={`form-control ${styles.formControl} ${
                                (nameValidation.nameFR.available === false && nameValidation.nameFR.message) ||
                                (touched.name_fr && errors.name_fr) ? styles.isInvalid :
                                (nameValidation.nameFR.available === true && values.name_fr.trim()) ? styles.isValid : ''
                              }`}
                              name="name_fr"
                              value={values.name_fr}
                              onChange={(e) => {
                                handleChange(e);
                                // Trigger debounced validation
                                debouncedValidation(values.name_en, e.target.value);
                              }}
                              onBlur={handleBlur}
                            />
                          )}
                          {frenchNameError && (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {frenchNameError}
                            </div>
                          )}
                          {nameValidation.nameFR.checking && (
                            <div className={`d-flex align-items-center mt-1 ${styles.validationSpinner}`}>
                              <div className={`spinner-border spinner-border-sm me-2 ${styles.validationSpinnerIcon}`} role="status">
                                <span className="visually-hidden">Loading...</span>
                              </div>
                              Checking availability...
                            </div>
                          )}
                          {nameValidation.nameFR.available === false && nameValidation.nameFR.message ? (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {nameValidation.nameFR.message}
                            </div>
                          ) : nameValidation.nameFR.available === true && values.name_fr.trim() && !nameValidation.nameFR.checking ? (
                            <div className={`valid-feedback d-block ${styles.validFeedback}`}>
                              French name is available
                            </div>
                          ) : touched.name_fr && errors.name_fr && (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {errors.name_fr}
                            </div>
                          )}
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            Brand
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            name="brand"
                            value={values.brand}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            Model
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            name="model"
                            value={values.model}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </Col>
                        <Col xs={12} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            Image URL
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl} ${touched.image_link && errors.image_link ? styles.isInvalid : ''}`}
                            name="image_link"
                            value={values.image_link}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                          {touched.image_link && errors.image_link && (
                            <div className={`invalid-feedback d-block ${styles.invalidFeedback}`}>
                              {errors.image_link}
                            </div>
                          )}
                        </Col>

                        {/* Image Upload Section */}
                        <Col xs={12} className="mb-3">
                          <h6 className={styles.imageUploadSection}>
                            📷 Upload New Image
                          </h6>
                          <div className={styles.imageUploadControls}>
                            <input
                              accept="image/*"
                              className={styles.imageUploadInput}
                              id="image-upload"
                              type="file"
                              onChange={handleImageChange}
                            />
                            <label
                              htmlFor="image-upload"
                              className={styles.imageUploadLabel}
                            >
                              <UploadIcon style={{ marginRight: '8px' }} />
                              Choose Image
                            </label>
                            {imageFile && (
                              <Button
                                variant="success"
                                onClick={uploadImage}
                                disabled={uploadingImage}
                                className={styles.uploadButton}
                              >
                                {uploadingImage ? (
                                  <>
                                    <Spinner size="sm" className="me-2" />
                                    Uploading...
                                  </>
                                ) : (
                                  'Upload'
                                )}
                              </Button>
                            )}
                          </div>

                          {/* Image Preview */}
                          {(imagePreview || equipment?.image_link) && (
                            <div className={styles.imagePreviewContainer}>
                              {imagePreview && (
                                <div className={styles.imagePreviewSection}>
                                  <small className={`text-muted d-block mb-2 ${styles.imagePreviewLabel}`}>
                                    New Image Preview:
                                  </small>
                                  <img
                                    src={imagePreview}
                                    alt="Preview"
                                    className={styles.imagePreview}
                                  />
                                </div>
                              )}
                              {equipment?.image_link && (
                                <div className={styles.imagePreviewSection}>
                                  <small className={`text-muted d-block mb-2 ${styles.imagePreviewLabel}`}>
                                    {imagePreview ? 'Current Image:' : 'Current Image:'}
                                  </small>
                                  <img
                                    src={equipment.image_link}
                                    alt="Current"
                                    className={styles.currentImage}
                                  />
                                </div>
                              )}
                            </div>
                          )}
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>



                {/* Descriptions */}
                <Col xs={12}>
                  <Card className={styles.card}>
                    <Card.Body className={styles.cardBody}>
                      <h5 className={styles.sectionTitle}>
                        📝 Descriptions
                      </h5>

                      <div className="d-flex justify-content-end mb-2">
                        <Button
                          variant="outline-info"
                          size="sm"
                          onClick={() => generateDescription(setFieldValue, values)}
                          disabled={generatingDescription || !values.name_en}
                          title="Generate bilingual descriptions using AI"
                          className={styles.generateButtonContainer}
                        >
                          <>
                            <span className={generatingDescription ? styles.spinningRobot : ''}>
                              🤖
                            </span>
                            {generatingDescription ? ' Generating...' : ' Generate Descriptions'}
                          </>
                        </Button>
                      </div>

                      {descriptionError && (
                        <Alert variant="danger" className="mb-3">
                          {descriptionError}
                        </Alert>
                      )}
                      <Row>
                        <Col xs={12} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            General Description
                          </label>
                          <textarea
                            className={`form-control ${styles.textareaControl}`}
                            rows={3}
                            name="description"
                            value={values.description}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            English Description
                          </label>
                          <textarea
                            className={`form-control ${styles.textareaControl}`}
                            rows={3}
                            name="description_en"
                            value={values.description_en}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            French Description
                          </label>
                          <textarea
                            className={`form-control ${styles.textareaControl}`}
                            rows={3}
                            name="description_fr"
                            value={values.description_fr}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Categories - Hidden per user request */}
                {/*
                <Col xs={12}>
                  <Card className={styles.card}>
                    <Card.Body className={styles.cardBody}>
                      <h5 className={styles.sectionTitle}>
                        📂 Categories
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            Categories
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            name="category"
                            value={Array.isArray(values.category) ? values.category.join(', ') : values.category || ''}
                            onChange={(e) => {
                              const categories = e.target.value.split(',').map(cat => cat.trim()).filter(cat => cat);
                              setFieldValue('category', categories);
                            }}
                            onBlur={handleBlur}
                            placeholder="Enter categories separated by commas"
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            Sub Categories
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            name="sub_category"
                            value={Array.isArray(values.sub_category) ? values.sub_category.join(', ') : values.sub_category || ''}
                            onChange={(e) => {
                              const subCategories = e.target.value.split(',').map(cat => cat.trim()).filter(cat => cat);
                              setFieldValue('sub_category', subCategories);
                            }}
                            onBlur={handleBlur}
                            placeholder="Enter sub categories separated by commas"
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>
                */}

                {/* Aliases */}
                <Col xs={12}>
                  <Card className={styles.card}>
                    <Card.Body className={styles.cardBody}>
                      <h5 className={styles.sectionTitle}>
                        🏷️ Aliases
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            English Aliases
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            value={Array.isArray(values.alias?.en) ? values.alias.en.join(', ') : ''}
                            onChange={(e) => {
                              const aliases = e.target.value.split(',').map(alias => alias.trim()).filter(alias => alias);
                              setFieldValue('alias.en', aliases);
                            }}
                            placeholder="Enter English aliases separated by commas"
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            French Aliases
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl}`}
                            value={Array.isArray(values.alias?.fr) ? values.alias.fr.join(', ') : ''}
                            onChange={(e) => {
                              const aliases = e.target.value.split(',').map(alias => alias.trim()).filter(alias => alias);
                              setFieldValue('alias.fr', aliases);
                            }}
                            placeholder="Enter French aliases separated by commas"
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* ARA Classification */}
                <Col xs={12}>
                  <Card className={styles.card}>
                    <Card.Body className={styles.cardBody}>
                      <h5 className={styles.sectionTitle}>
                        🏗️ ARA Classification
                        <small className="text-muted ms-2">(System Generated - Read Only)</small>
                      </h5>
                      <Row>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            ARA Level 1 Category
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl} ${styles.readOnlyField}`}
                            name="ara_level1_name"
                            value={values.ara_level1_name}
                            readOnly
                            tabIndex="-1"
                          />
                        </Col>
                        <Col md={6} className="mb-3">
                          <label className={`form-label ${styles.formLabel}`}>
                            ARA Level 2 Type
                          </label>
                          <input
                            type="text"
                            className={`form-control ${styles.formControl} ${styles.readOnlyField}`}
                            name="ara_level2_name"
                            value={values.ara_level2_name}
                            readOnly
                            tabIndex="-1"
                          />
                        </Col>
                      </Row>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Action Buttons */}
                <Col xs={12}>
                  <div className={styles.actionButtons}>
                    <Button
                      variant="outline-secondary"
                      onClick={() => navigate('/admin/dashboard')}
                      disabled={isSubmitting || saving}
                      className={styles.cancelButton}
                    >
                      <CancelIcon className="me-2" />
                      Cancel
                    </Button>
                    {/* Classify Button - Only show if equipment has no classification */}
                    {!hasClassification(equipment) && (
                      <Button
                        onClick={classifyEquipment}
                        disabled={isSubmitting || saving || classifying}
                        style={{
                          backgroundColor: '#17a2b8',
                          color: 'white',
                          border: '2px solid #17a2b8',
                          borderRadius: '8px',
                          fontWeight: '500',
                          padding: '12px 24px',
                          transition: 'all 0.2s ease-in-out',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px'
                        }}
                        onMouseEnter={(e) => {
                          e.target.style.backgroundColor = '#138496';
                          e.target.style.borderColor = '#117a8b';
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.backgroundColor = '#17a2b8';
                          e.target.style.borderColor = '#17a2b8';
                        }}
                        title="Classify equipment using AI"
                      >
                        {classifying ? (
                          <>
                            <Spinner size="sm" />
                            <span>Classifying...</span>
                          </>
                        ) : (
                          <>
                            <span style={{ fontSize: '14px' }}>🤖</span>
                            <span>Classify</span>
                          </>
                        )}
                      </Button>
                    )}
                    <Button
                      type="submit"
                      variant="primary"
                      disabled={isSubmitting || saving}
                      onClick={(e) => {
                        e.preventDefault();
                        submitForm();
                      }}
                      className={styles.submitButton}
                    >
                      {saving ? (
                        <>
                          <Spinner size="sm" className="me-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <SaveIcon className="me-2" />
                          Save Changes
                        </>
                      )}
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          );
          }}
        </Formik>
      </Container>
    </Container>
  );
}

export default MasterEquipmentUpdateModern;
