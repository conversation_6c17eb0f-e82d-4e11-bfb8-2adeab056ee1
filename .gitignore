# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# go
vendor/

# dependencies
/node_modules
/.pnp
.pnp.js
/public

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

### VisualStudioCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

### Firebase ###
.idea
**/node_modules/*
**/.firebaserc
**/functions/lib
key.json
**/.runtimeconfig.json
*.cache
package-lock.json
yarn.lock
.vscode/settings.json
.prettierrc.json
.vscode/extensions.json
.vscode/launch.json
backend/.vscode/settings.json
backend/.vscode/launch.json
backend/__debug_bin.exe

#augment
.augment/*

backend/cmd/tooler-api/tooler-api
backend/ara-classifier