package api

import (
	"context"
	"encoding/csv"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/models"
	"github.com/vima-inc/derental/service"
)

// adminSignin handles admin authentication with domain validation
func adminSignin(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}

		err := c.BindJSON(&body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		// Validate domain before attempting authentication
		if !isValidAdminDomain(body.Email) {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access restricted to @derentalequipment.com domain"})
			return
		}

		resp, err := svc.AdminSignIn(c.Request.Context(), body.Email, body.Password)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// getMasterInventory returns all master inventory items for admin dashboard
func getMasterInventory(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		masterInventory, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  masterInventory,
			"total": len(masterInventory),
		})
	}
}

// getAdminDashboardStats returns dashboard statistics for admin
func getAdminDashboardStats(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		stats, err := svc.GetAdminDashboardStats(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, stats)
	}
}

// isValidAdminDomain checks if email has the required domain
func isValidAdminDomain(email string) bool {
	return len(email) > 22 && email[len(email)-22:] == "@derentalequipment.com"
}

// getARALevel1Categories returns all ARA Level 1 categories for admin filters
func getARALevel1Categories(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		categories, err := svc.GetAllARALevel1Categories(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  categories,
			"total": len(categories),
		})
	}
}

// getARALevel2Types returns all ARA Level 2 types for admin filters
func getARALevel2Types(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		types, err := svc.GetAllARALevel2Types(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"data":  types,
			"total": len(types),
		})
	}
}

// getMasterEquipmentByID returns a single master equipment item by ID for admin editing
func getMasterEquipmentByID(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		equipment, err := svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, equipment)
	}
}

// createMasterEquipment creates a new master equipment item for admin
func createMasterEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		var equipment models.ToolerBidzEquipment
		err := c.BindJSON(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			_ = c.Error(err)
			return
		}

		// Validate the equipment data
		validationErrors := validateMasterEquipment(c.Request.Context(), svc, equipment, "")
		if len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Validation failed",
				"errors": validationErrors,
			})
			return
		}

		// Create the equipment
		err = svc.AddToolerBidzEquipment(c.Request.Context(), &equipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create equipment: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": "Master equipment created successfully",
			"id":      equipment.ID,
		})
	}
}

// normalizeEquipmentName normalizes equipment names for comparison
func normalizeEquipmentName(name string) string {
	// Convert to lowercase
	normalized := strings.ToLower(name)

	// Remove extra whitespace and normalize spaces
	normalized = strings.TrimSpace(normalized)
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")

	// Remove common punctuation and special characters
	normalized = regexp.MustCompile(`[^\p{L}\p{N}\s]`).ReplaceAllString(normalized, "")

	// Remove extra spaces again after punctuation removal
	normalized = strings.TrimSpace(normalized)
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")

	return normalized
}

// levenshteinDistance calculates the Levenshtein distance between two strings
func levenshteinDistance(s1, s2 string) int {
	if len(s1) == 0 {
		return len(s2)
	}
	if len(s2) == 0 {
		return len(s1)
	}

	// Create a matrix to store distances
	matrix := make([][]int, len(s1)+1)
	for i := range matrix {
		matrix[i] = make([]int, len(s2)+1)
	}

	// Initialize first row and column
	for i := 0; i <= len(s1); i++ {
		matrix[i][0] = i
	}
	for j := 0; j <= len(s2); j++ {
		matrix[0][j] = j
	}

	// Fill the matrix
	for i := 1; i <= len(s1); i++ {
		for j := 1; j <= len(s2); j++ {
			cost := 0
			if s1[i-1] != s2[j-1] {
				cost = 1
			}

			matrix[i][j] = min3(
				matrix[i-1][j]+1,      // deletion
				matrix[i][j-1]+1,      // insertion
				matrix[i-1][j-1]+cost, // substitution
			)
		}
	}

	return matrix[len(s1)][len(s2)]
}

// min returns the minimum of three integers
func min3(a, b, c int) int {
	return min(a, min(b, c))
}

// calculateSimilarityScore calculates similarity score between two strings (0.0 to 1.0)
func calculateSimilarityScore(s1, s2 string) float64 {
	if s1 == s2 {
		return 1.0
	}

	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}

	if maxLen == 0 {
		return 1.0
	}

	distance := levenshteinDistance(s1, s2)
	return 1.0 - float64(distance)/float64(maxLen)
}

// checkEquipmentNameSimilarity checks if two equipment names are too similar using multiple criteria
func checkEquipmentNameSimilarity(name1, name2 string) bool {
	norm1 := normalizeEquipmentName(name1)
	norm2 := normalizeEquipmentName(name2)

	// Skip empty names
	if len(norm1) == 0 || len(norm2) == 0 {
		return false
	}

	// Exact match after normalization
	if norm1 == norm2 {
		return true
	}

	// Check substring containment (for cases like "rivet buster" vs "rivet")
	// But be more intelligent about it - avoid flagging specific equipment names that contain generic words
	if strings.Contains(norm1, norm2) || strings.Contains(norm2, norm1) {
		// Only consider it similar if the shorter name is at least 3 characters
		minLen := len(norm1)
		if len(norm2) < minLen {
			minLen = len(norm2)
		}

		// Additional check: if one name is much longer than the other, it's likely a specific vs generic case
		// Don't flag if the longer name is more than 2x the length of the shorter name
		maxLen := len(norm1)
		if len(norm2) > maxLen {
			maxLen = len(norm2)
		}

		lengthRatio := float64(maxLen) / float64(minLen)

		if minLen >= 3 && lengthRatio <= 2.0 {
			return true
		}
	}

	// Calculate similarity score using Levenshtein distance
	similarityScore := calculateSimilarityScore(norm1, norm2)

	// Use balanced thresholds to catch typos but avoid false positives
	minLen := len(norm1)
	if len(norm2) < minLen {
		minLen = len(norm2)
	}

	// Balanced thresholds:
	// - Short names (4-8 chars): 90% similarity required (catches close typos)
	// - Medium names (9-15 chars): 85% similarity required
	// - Long names (16+ chars): 80% similarity required (specific names need lower threshold)
	// - Very long names (25+ chars): 75% similarity required (very specific equipment names)

	if minLen >= 4 && minLen <= 8 && similarityScore >= 0.90 {
		return true
	}

	if minLen >= 9 && minLen <= 15 && similarityScore >= 0.85 {
		return true
	}

	if minLen >= 16 && minLen <= 24 && similarityScore >= 0.80 {
		return true
	}

	if minLen >= 25 && similarityScore >= 0.75 {
		return true
	}

	return false
}

// validateMasterEquipment validates the master equipment data
func validateMasterEquipment(ctx context.Context, svc *service.Service, equipment models.ToolerBidzEquipment, excludeID string) []string {
	var errors []string

	// Required fields validation
	if strings.TrimSpace(equipment.NameEN) == "" {
		errors = append(errors, "English name is required")
	}
	// French name is optional

	// Check for similar English names
	if strings.TrimSpace(equipment.NameEN) != "" {
		allEquipment, err := svc.GetAllToolerBidzEquipment(ctx)
		if err == nil {
			for _, existing := range allEquipment {
				if existing.ID != excludeID && checkEquipmentNameSimilarity(equipment.NameEN, existing.NameEN) {
					errors = append(errors, "Equipment with similar English name already exists: "+existing.NameEN)
					break
				}
			}
		}
	}

	// Check for similar French names
	if strings.TrimSpace(equipment.NameFR) != "" {
		allEquipment, err := svc.GetAllToolerBidzEquipment(ctx)
		if err == nil {
			for _, existing := range allEquipment {
				if existing.ID != excludeID && checkEquipmentNameSimilarity(equipment.NameFR, existing.NameFR) {
					errors = append(errors, "Equipment with similar French name already exists: "+existing.NameFR)
					break
				}
			}
		}
	}

	// URL validation for image link
	if equipment.ImageLink != "" && !strings.HasPrefix(equipment.ImageLink, "http") {
		errors = append(errors, "Image link must be a valid URL")
	}

	// ARA classification validation
	if equipment.ARALevel1ID < 0 {
		errors = append(errors, "ARA Level 1 ID must be non-negative")
	}
	if equipment.ARALevel2ID < 0 {
		errors = append(errors, "ARA Level 2 ID must be non-negative")
	}

	return errors
}

// checkEquipmentNameAvailability checks if equipment names are available
func checkEquipmentNameAvailability(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		var request struct {
			NameEN    string `json:"name_en"`
			NameFR    string `json:"name_fr"`
			ExcludeID string `json:"exclude_id,omitempty"`
		}

		err := c.BindJSON(&request)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			return
		}

		response := gin.H{
			"name_en_available": true,
			"name_fr_available": true,
			"errors":            []string{},
		}

		var errors []string

		// Check English name availability
		if strings.TrimSpace(request.NameEN) != "" {
			allEquipment, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
			if err == nil {
				for _, existing := range allEquipment {
					if existing.ID != request.ExcludeID && checkEquipmentNameSimilarity(request.NameEN, existing.NameEN) {
						response["name_en_available"] = false
						errors = append(errors, "Equipment with similar English name already exists: "+existing.NameEN)
						break
					}
				}
			}
		}

		// Check French name availability
		if strings.TrimSpace(request.NameFR) != "" {
			allEquipment, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
			if err == nil {
				for _, existing := range allEquipment {
					if existing.ID != request.ExcludeID && checkEquipmentNameSimilarity(request.NameFR, existing.NameFR) {
						response["name_fr_available"] = false
						errors = append(errors, "Equipment with similar French name already exists: "+existing.NameFR)
						break
					}
				}
			}
		}

		response["errors"] = errors
		c.JSON(http.StatusOK, response)
	}
}

// updateMasterEquipment updates a master equipment item for admin
func updateMasterEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		var equipment models.ToolerBidzEquipment
		err := c.BindJSON(&equipment)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			_ = c.Error(err)
			return
		}

		// Ensure the ID matches the URL parameter
		equipment.ID = equipmentID

		// Get existing equipment to compare names
		existingEquipment, err := svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Equipment not found"})
			_ = c.Error(err)
			return
		}

		// Only validate names if they have changed
		var validationErrors []string

		// Required fields validation
		if strings.TrimSpace(equipment.NameEN) == "" {
			validationErrors = append(validationErrors, "English name is required")
		}

		// Check for similar English names only if name has changed
		if strings.TrimSpace(equipment.NameEN) != "" &&
			strings.TrimSpace(equipment.NameEN) != strings.TrimSpace(existingEquipment.NameEN) {
			allEquipment, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
			if err == nil {
				for _, existing := range allEquipment {
					if existing.ID != equipmentID && checkEquipmentNameSimilarity(equipment.NameEN, existing.NameEN) {
						validationErrors = append(validationErrors, "Equipment with similar English name already exists: "+existing.NameEN)
						break
					}
				}
			}
		}

		// Check for similar French names only if name has changed
		if strings.TrimSpace(equipment.NameFR) != "" &&
			strings.TrimSpace(equipment.NameFR) != strings.TrimSpace(existingEquipment.NameFR) {
			allEquipment, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
			if err == nil {
				for _, existing := range allEquipment {
					if existing.ID != equipmentID && checkEquipmentNameSimilarity(equipment.NameFR, existing.NameFR) {
						validationErrors = append(validationErrors, "Equipment with similar French name already exists: "+existing.NameFR)
						break
					}
				}
			}
		}

		// URL validation for image link
		if equipment.ImageLink != "" && !strings.HasPrefix(equipment.ImageLink, "http") {
			validationErrors = append(validationErrors, "Image link must be a valid URL")
		}

		// ARA classification validation
		if equipment.ARALevel1ID < 0 {
			validationErrors = append(validationErrors, "ARA Level 1 ID must be non-negative")
		}
		if equipment.ARALevel2ID < 0 {
			validationErrors = append(validationErrors, "ARA Level 2 ID must be non-negative")
		}

		if len(validationErrors) > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":  "Validation failed",
				"errors": validationErrors,
			})
			return
		}

		err = svc.UpdateToolerBidzEquipment(c.Request.Context(), equipment)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update equipment: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Master equipment updated successfully"})
	}
}

// uploadMasterEquipmentImage uploads an image for master equipment
func uploadMasterEquipmentImage(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		file, header, err := c.Request.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File upload failed: " + err.Error()})
			_ = c.Error(err)
			return
		}
		defer file.Close()

		// Validate file type
		allowedTypes := map[string]bool{
			"image/jpeg": true,
			"image/jpg":  true,
			"image/png":  true,
			"image/gif":  true,
			"image/webp": true,
		}

		contentType := header.Header.Get("Content-Type")
		if !allowedTypes[contentType] {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed"})
			return
		}

		// Validate file size (max 10MB)
		const maxFileSize = 10 * 1024 * 1024 // 10MB
		if header.Size > maxFileSize {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File size too large. Maximum size is 10MB"})
			return
		}

		// Check if equipment exists
		_, err = svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Equipment not found"})
			_ = c.Error(err)
			return
		}

		imageURL, err := svc.UploadAdminMasterEquipmentImage(c.Request.Context(), equipmentID, header.Filename, file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload image: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message":   "Image uploaded successfully",
			"image_url": imageURL,
		})
	}
}

// exportMasterInventoryCSV exports all master inventory data as CSV
func exportMasterInventoryCSV(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		masterInventory, err := svc.GetAllToolerBidzEquipment(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			_ = c.Error(err)
			return
		}

		// Generate filename with current datetime
		currentDateTime := time.Now().Format("2006-01-02_15-04-05")
		filename := fmt.Sprintf("master_inventory_%s.csv", currentDateTime)

		// Set headers for CSV download
		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
		c.Header("Cache-Control", "no-cache")

		// Create CSV writer
		writer := csv.NewWriter(c.Writer)
		defer writer.Flush()

		// Write CSV header
		header := []string{
			"ID",
			"Name (EN)",
			"Name (FR)",
			"Description",
			"Brand",
			"Model",
			"ARA Level 1",
			"ARA Level 2",
			"Image Link",
		}
		if err := writer.Write(header); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write CSV header"})
			return
		}

		// Write data rows
		for _, item := range masterInventory {
			row := []string{
				item.ID,
				item.NameEN,
				item.NameFR,
				item.Description,
				item.Brand,
				item.Model,
				item.ARALevel1Name,
				item.ARALevel2Name,
				item.ImageLink,
			}
			if err := writer.Write(row); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to write CSV row"})
				return
			}
		}
	}
}

// generateEquipmentDescription generates AI-powered description for equipment
func generateEquipmentDescription(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		var request models.DescriptionGenerationRequest
		err := c.BindJSON(&request)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			_ = c.Error(err)
			return
		}

		// Validate equipment name
		if strings.TrimSpace(request.EquipmentName) == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment name is required"})
			return
		}

		response, err := svc.GenerateEquipmentDescription(c.Request.Context(), request.EquipmentName, request.ImageURL)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate description: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, response)
	}
}

// generateFrenchName generates AI-powered French name for equipment
func generateFrenchName(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		var request models.FrenchNameGenerationRequest
		err := c.BindJSON(&request)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format: " + err.Error()})
			_ = c.Error(err)
			return
		}

		// Validate equipment name
		if strings.TrimSpace(request.EnglishName) == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "English name is required"})
			return
		}

		response, err := svc.GenerateFrenchName(c.Request.Context(), request.EnglishName)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate French name: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, response)
	}
}

// classifyMasterEquipment classifies a specific equipment item using ARA classification
func classifyMasterEquipment(svc *service.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user email from context (set by AdminAuthorization middleware)
		email := c.Request.Context().Value(middleware.EmailKey)
		if email == nil {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		equipmentID := c.Param("id")
		if equipmentID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Equipment ID is required"})
			return
		}

		// Check if equipment exists before classifying
		_, err := svc.GetBidzEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Equipment not found"})
			_ = c.Error(err)
			return
		}

		// Classify the equipment
		stats, err := svc.ClassifyEquipmentByID(c.Request.Context(), equipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to classify equipment: " + err.Error()})
			_ = c.Error(err)
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "Equipment classified successfully",
			"stats":   stats,
		})
	}
}
